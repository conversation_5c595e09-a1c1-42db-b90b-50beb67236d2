import { AnyBulkWriteOperation, ObjectId } from 'mongodb';
import { Event, EventApplicationModule, EventApplicationModuleConfirmEmailContents, ModuleType } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '317_fixEventEmailContentTranslationKeys',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        console.log('Starting migration: Fix event email content translation keys');

        // Find events with translation key values instead of actual content
        const problematicEvents = await db
            .collection<Event>('events')
            .find({
                'emailContents.submitOrder.subject.defaultValue.defaultValue': "t('emails:event.submitOrder.subject')",
                'emailContents.submitOrder.introTitle.defaultValue.defaultValue':
                    "t('emails:event.submitOrder.introTitle')",
                'emailContents.submitOrder.contentText.defaultValue.defaultValue':
                    "t('emails:event.submitOrder.contentText')",
                'emailContents.submitOrder.introImage': null,
            })
            .toArray();

        console.log(`Found ${problematicEvents.length} events with translation key values`);

        if (problematicEvents.length === 0) {
            console.log('No events need to be updated. Migration completed.');

            return;
        }

        // Get unique module IDs to fetch modules in batch
        const moduleIds = Array.from(new Set(problematicEvents.map(event => event.moduleId)));
        console.log(`Fetching ${moduleIds.length} unique modules`);

        // Fetch all required modules
        const modules = await db
            .collection<EventApplicationModule>('modules')
            .find({
                _id: { $in: moduleIds },
                _type: ModuleType.EventApplicationModule,
            })
            .toArray();

        console.log(`Found ${modules.length} modules`);

        // Create a map for quick module lookup
        const moduleMap = new Map<string, EventApplicationModule>();
        modules.forEach(module => {
            moduleMap.set(module._id.toString(), module);
        });

        // Prepare bulk write operations
        const bulkWriteOps: AnyBulkWriteOperation[] = [];
        let updatedCount = 0;
        let skippedCount = 0;

        for (const event of problematicEvents) {
            const module = moduleMap.get(event.moduleId.toString());

            if (!module) {
                console.warn(`Module not found for event ${event._id} (moduleId: ${event.moduleId})`);
                skippedCount++;
                continue;
            }

            if (!module.emailContents?.submitOrder?.defaultValue) {
                console.warn(`Module ${module._id} does not have email content defaults`);
                skippedCount++;
                continue;
            }

            const moduleDefaults = module.emailContents.submitOrder.defaultValue;

            // Verify module has the required fields
            if (
                !moduleDefaults.subject?.defaultValue?.defaultValue ||
                !moduleDefaults.introTitle?.defaultValue?.defaultValue ||
                !moduleDefaults.contentText?.defaultValue?.defaultValue
            ) {
                console.warn(`Module ${module._id} has incomplete email content defaults`);
                skippedCount++;
                continue;
            }

            console.log(`Updating event ${event._id}:`);
            console.log(`  - Subject: "${moduleDefaults.subject.defaultValue.defaultValue}"`);

            bulkWriteOps.push({
                updateOne: {
                    filter: { _id: event._id },
                    update: {
                        $set: {
                            'emailContents.submitOrder.subject.defaultValue.defaultValue':
                                moduleDefaults.subject.defaultValue.defaultValue,
                            'emailContents.submitOrder.introTitle.defaultValue.defaultValue':
                                moduleDefaults.introTitle.defaultValue.defaultValue,
                            'emailContents.submitOrder.contentText.defaultValue.defaultValue':
                                moduleDefaults.contentText.defaultValue.defaultValue,
                        },
                    },
                },
            });

            updatedCount++;
        }

        // Execute bulk write operations
        // if (bulkWriteOps.length > 0) {
        //     console.log(`Executing bulk write with ${bulkWriteOps.length} operations`);
        //     const result = await db.collection('events').bulkWrite(bulkWriteOps);
        //     console.log(`Bulk write result: ${result.modifiedCount} documents modified`);
        // }

        // console.log(`Migration completed:`);
        // console.log(`  - Events updated: ${updatedCount}`);
        // console.log(`  - Events skipped: ${skippedCount}`);
        // console.log(`  - Total events processed: ${problematicEvents.length}`);

        // // Verification: Check if any problematic events still exist
        // const remainingProblematicEvents = await db.collection<Event>('events').countDocuments({
        //     'emailContents.submitOrder.subject.defaultValue.defaultValue': "t('emails:event.submitOrder.subject')",
        //     'emailContents.submitOrder.introTitle.defaultValue.defaultValue':
        //         "t('emails:event.submitOrder.introTitle')",
        //     'emailContents.submitOrder.contentText.defaultValue.defaultValue':
        //         "t('emails:event.submitOrder.contentText')",
        //     'emailContents.submitOrder.introImage': null,
        // });

        // if (remainingProblematicEvents > 0) {
        //     console.warn(`Warning: ${remainingProblematicEvents} events still have translation key values`);
        // } else {
        //     console.log('✅ Verification passed: No events with translation key values remain');
        // }
    },
};
