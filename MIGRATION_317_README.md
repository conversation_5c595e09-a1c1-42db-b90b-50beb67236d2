# Migration 317: Update Event Email Contents Submit Order with Module Values

## Overview

This migration updates event documents in the `events` collection where `emailContents.submitOrder` contains default translation keys, replacing them with the actual values from their associated modules.

## Problem

Some events have `emailContents.submitOrder` with default translation key values like:

```javascript
{
    subject: {
        defaultValue: "t('emails:event.submitOrder.subject')",
        overrides: [],
    },
    introTitle: {
        defaultValue: "t('emails:event.submitOrder.introTitle')",
        overrides: [],
    },
    contentText: {
        defaultValue: "t('emails:event.submitOrder.contentText')",
        overrides: [],
    },
}
```

These should be replaced with the actual content from their associated EventApplicationModule.

## Solution

The migration:

1. **Identifies affected events**: Finds events where `emailContents.submitOrder` contains default translation key values like `"t('emails:event.submitOrder.subject')"` or `'emails:event.submitOrder.subject'`
2. **Retrieves correct data**: Looks up each event's associated module via `moduleId` and extracts the actual email contents from `module.emailContents.submitOrder.defaultValue`
3. **Updates events**: Replaces the default translation keys with the actual content from the module
4. **Error handling**: Handles cases where modules don't exist or have incomplete data
5. **Logging**: Provides detailed logging of the migration process and results

## Expected Structure After Migration

Events should have `emailContents.submitOrder` with actual content instead of translation keys:

```javascript
{
    subject: {
        defaultValue: "Actual subject text from module",
        overrides: [],
    },
    introTitle: {
        defaultValue: "Actual intro title text from module",
        overrides: [],
    },
    introImage: { /* optional */ },
    contentText: {
        defaultValue: "Actual content text from module",
        overrides: [],
    },
}
```

## Safety Features

- Uses `bulkWrite` with `ordered: false` for efficiency and partial failure tolerance
- Validates replacement data before applying updates
- Provides detailed logging for troubleshooting
- Does not modify events if their associated module is missing or invalid
- Counts and reports successful updates vs errors

## Execution

The migration will run automatically when the application starts and database migrations are executed. It can also be run manually using the migration system.

## Rollback

This migration does not include an automatic rollback. If rollback is needed, the original data structure would need to be restored from backups or reconstructed manually.
